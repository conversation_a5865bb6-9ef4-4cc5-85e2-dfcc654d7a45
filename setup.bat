@echo off
echo ========================================
echo Transport Management System Setup
echo ========================================
echo.

echo Step 1: Installing Backend Dependencies...
cd Backend
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install backend dependencies
    pause
    exit /b 1
)

echo.
echo Step 2: Generating Prisma Client...
call npx prisma generate
if %errorlevel% neq 0 (
    echo ERROR: Failed to generate Prisma client
    pause
    exit /b 1
)

echo.
echo Step 3: Running Database Migrations...
call npx prisma migrate deploy
if %errorlevel% neq 0 (
    echo ERROR: Failed to run database migrations
    echo Make sure PostgreSQL is running and DATABASE_URL is correct in Backend/.env
    pause
    exit /b 1
)

echo.
echo Step 4: Seeding Database...
call npm run seed
if %errorlevel% neq 0 (
    echo ERROR: Failed to seed database
    pause
    exit /b 1
)

echo.
echo Step 5: Installing Frontend Dependencies...
cd ..\Frontend
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install frontend dependencies
    pause
    exit /b 1
)

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo To start the application:
echo 1. Open Terminal 1 and run: cd Backend && npm start
echo 2. Open Terminal 2 and run: cd Frontend && npm run dev
echo 3. Open browser to: http://localhost:5173
echo.
echo Default login credentials:
echo Email: <EMAIL>
echo Password: admin123
echo.
pause
