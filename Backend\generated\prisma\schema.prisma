generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enum and model for ClientType with predefined values
enum ClientTypeCode {
  HTS // Home to School
  SEN // SEN Transport
  LAC // Looked-After Child
  DIS // Discretionary Transport
  PRIV // Private / Self-Funded
}

model ClientType {
  id          Int            @id @default(autoincrement()) @map("client_type_id")
  code        ClientTypeCode @unique
  name        String         @db.VarChar(100)
  description String?        @db.Text
  createdAt   DateTime       @default(now()) @map("created_at")
  updatedAt   DateTime       @updatedAt @map("updated_at")

  // Relations
  students Student[]

  @@map("client_types")
}

// Enum and model for MedicalNeed with predefined values
enum MedicalNeedCode {
  ASTHMA
  ASD
  DIABETES
  EPILEPSY
  ADHD
}

model MedicalNeed {
  id          Int             @id @default(autoincrement()) @map("need_id")
  code        MedicalNeedCode
  description String          @db.Var<PERSON>har(255)

  // Relations
  students Student[]

  @@map("medical_needs")
}

// New YearGroup enum and table with predefined values
enum YearGroupValue {
  YR_7
  YR_8
  YR_9
  YR_10
  YR_11
}

model YearGroup {
  id   Int            @id @default(autoincrement()) @map("year_group_id")
  code YearGroupValue @unique
  name String         @db.VarChar(20)

  // Relations
  students Student[]

  @@map("year_groups")
}

model Student {
  id             Int       @id @default(autoincrement()) @map("student_id")
  title          String?   @db.VarChar(10)
  firstName      String    @map("first_name") @db.VarChar(50)
  surname        String    @db.VarChar(50)
  knownAs        String?   @map("known_as") @db.VarChar(50)
  gender         String?   @db.Char(1)
  dateOfBirth    DateTime? @map("date_of_birth") @db.Date
  clientTypeId   Int?      @map("client_type_id")
  requiresTravel Boolean?  @map("requires_travel")
  inCare         Boolean?  @map("in_care")
  medicalNeedId  Int?      @map("medical_need_id")
  yearGroupId    Int?      @map("year_group_id") // Changed from string to int reference
  estabId        Int?      @map("estab_id")
  isActive       Boolean   @default(true) @map("is_active")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")

  // Relations
  clientType        ClientType?                   @relation(fields: [clientTypeId], references: [id])
  medicalNeed       MedicalNeed?                  @relation(fields: [medicalNeedId], references: [id])
  yearGroup         YearGroup?                    @relation(fields: [yearGroupId], references: [id]) // New relation
  establishment     Establishment?                @relation(fields: [estabId], references: [id])
  contacts          StudentContact[]
  travelAssignments StudentTravelAssignment[]
  codeAssignments   StudentCodeAssignment[]
  communications    StudentContactCommunication[]
  medicalRecords    StudentsMedical[]

  @@map("students")
}

model StudentContact {
  id           Int       @id @default(autoincrement()) @map("contact_id")
  studentId    Int       @map("student_id")
  title        String?   @db.VarChar(10)
  firstname    String    @db.VarChar(50)
  surname      String    @db.VarChar(50)
  knownAs      String?   @map("known_as") @db.VarChar(50)
  relationship String?   @db.VarChar(20)
  dateOfBirth  DateTime? @map("date_of_birth") @db.Date
  isPrimary    Boolean   @map("is_primary")
  isPayer      Boolean   @map("is_payer")
  // Removed password, resetToken, resetTokenExpiry fields

  // Relations
  student        Student                       @relation(fields: [studentId], references: [id])
  contactMethods StudentContactMethod[]
  addresses      StudentContactAddress[]
  notes          StudentContactNote[]
  communications StudentContactCommunication[]

  @@map("students_contacts")
}

model StudentContactMethod {
  id         Int    @id @default(autoincrement()) @map("method_id")
  contactId  Int    @map("contact_id")
  methodType String @map("method_type") @db.VarChar(20)
  value      String @db.VarChar(100)

  // Relations
  contact StudentContact @relation(fields: [contactId], references: [id])

  @@map("student_contact_methods")
}

model StudentContactAddress {
  id           Int       @id @default(autoincrement()) @map("addr_id")
  contactId    Int       @map("contact_id")
  addressLine1 String    @map("address_line1") @db.VarChar(255)
  addressLine2 String?   @map("address_line2") @db.VarChar(255)
  city         String    @db.VarChar(100)
  county       String?   @db.VarChar(100)
  postcode     String    @db.VarChar(10)
  validFrom    DateTime  @map("valid_from") @db.Date
  validTo      DateTime? @map("valid_to") @db.Date
  isPrimary    Boolean   @map("is_primary")
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")
  updatedBy    Int?      @map("updated_by")

  // Relations
  contact         StudentContact   @relation(fields: [contactId], references: [id])
  updatedByStaff  Staff?           @relation(fields: [updatedBy], references: [id])
  travelLocations TravelLocation[]

  @@map("student_contact_address")
}

model StudentContactNote {
  id        Int      @id @default(autoincrement()) @map("note_id")
  contactId Int      @map("contact_id")
  authorId  Int      @map("author_id")
  content   String   @db.Text
  createdAt DateTime @default(now()) @map("created_at")
  noteType  String?  @map("note_type") @db.VarChar(20)

  // Relations
  contact StudentContact @relation(fields: [contactId], references: [id])
  author  Staff          @relation(fields: [authorId], references: [id])

  @@map("student_contact_notes")
}

model StudentContactCommunication {
  id        Int      @id @default(autoincrement()) @map("comm_id")
  studentId Int      @map("student_id")
  contactId Int      @map("contact_id")
  authorId  Int      @map("author_id")
  commType  String   @map("comm_type") @db.VarChar(20)
  commGroup String   @map("comm_group") @db.VarChar(20)
  subject   String   @db.VarChar(255)
  content   String   @db.Text
  sentAt    DateTime @map("sent_at")

  // Relations
  student Student        @relation(fields: [studentId], references: [id])
  contact StudentContact @relation(fields: [contactId], references: [id])
  author  Staff          @relation(fields: [authorId], references: [id])

  @@map("student_contact_communication")
}

model Establishment {
  id             Int      @id @default(autoincrement()) @map("estab_id")
  name           String   @db.VarChar(100)
  addressLine1   String?  @map("address_line1") @db.VarChar(255)
  city           String?  @db.VarChar(100)
  postcode       String?  @db.VarChar(20)
  schoolType     String?  @map("school_type") @db.VarChar(50)
  dfesNumber     String?  @map("dfes_number") @db.VarChar(20)
  leaNumber      String?  @map("lea_number") @db.VarChar(20)
  localAuthority String?  @map("local_authority") @db.VarChar(100)
  telephone      String?  @db.VarChar(20)
  email          String?  @db.VarChar(100)
  notes          String?  @db.Text
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // Relations
  students        Student[]
  termPeriods     EstablishmentTermPeriod[]
  termTimes       SchoolTermTime[]
  travelLocations TravelLocation[]

  @@map("establishments")
}

model StudentTravelAssignment {
  id                 Int      @id @default(autoincrement()) @map("assignment_id")
  studentId          Int      @map("student_id")
  operatorContractId Int      @map("operator_contract_id")
  termId             Int      @map("term_id")
  runType            String   @map("run_type") @db.VarChar(2)
  maxTravelTimeMins  Int?     @map("max_travel_time_mins")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")

  // Relations
  student          Student                 @relation(fields: [studentId], references: [id])
  operatorContract OperatorContract        @relation(fields: [operatorContractId], references: [id])
  termPeriod       EstablishmentTermPeriod @relation(fields: [termId], references: [id])
  travelLocations  TravelLocation[]
  travelLogs       StudentTravelLog[]
  travelDays       StudentTravelDay[]
  routeMetrics     TravelRouteMetric[]

  @@map("student_travel_assignments")
}

model TransportOperator {
  id        Int      @id @default(autoincrement()) @map("operator_id")
  name      String   @db.VarChar(100)
  phone     String?  @db.VarChar(20)
  email     String?  @db.VarChar(100)
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  operatorContracts OperatorContract[]

  @@map("transport_operators")
}

model OperatorContract {
  id          Int       @id @default(autoincrement()) @map("contract_id")
  operatorId  Int       @map("operator_id")
  code        String    @unique @db.VarChar(20)
  name        String    @db.VarChar(100)
  maxStudents Int?      @map("max_students")
  validFrom   DateTime  @map("valid_from") @db.Date
  validTo     DateTime? @map("valid_to") @db.Date
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  // Relations
  operator          TransportOperator                  @relation(fields: [operatorId], references: [id])
  travelAssignments StudentTravelAssignment[]
  incidents         ContractIncident[]
  budgetAssignments OperatorContractBudgetAssignment[]

  @@map("operator_contracts")
}

model OperatorContractBudgetAssignment {
  ocbaId     Int       @id @default(autoincrement()) @map("ocba_id")
  contractId Int       @map("contract_id")
  budgetCode String    @map("budget_code") @db.VarChar(20)
  validFrom  DateTime  @map("valid_from") @db.Date
  validTo    DateTime? @map("valid_to") @db.Date
  createdBy  Int?      @map("created_by")
  createdAt  DateTime  @default(now()) @map("created_at")
  updatedBy  Int?      @map("updated_by")
  updatedAt  DateTime  @updatedAt @map("updated_at")

  // Relations
  contract       OperatorContract @relation(fields: [contractId], references: [id])
  budget         BudgetCode       @relation(fields: [budgetCode], references: [code])
  createdByStaff Staff?           @relation("ContractBudgetCreatedBy", fields: [createdBy], references: [id])
  updatedByStaff Staff?           @relation("ContractBudgetUpdatedBy", fields: [updatedBy], references: [id])

  @@map("operator_contract_budget_assignments")
}

model EstablishmentTermPeriod {
  id        Int      @id @default(autoincrement()) @map("term_id")
  estabId   Int      @map("estab_id")
  name      String   @db.VarChar(100)
  validFrom DateTime @map("valid_from") @db.Date
  validTo   DateTime @map("valid_to") @db.Date
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  establishment     Establishment             @relation(fields: [estabId], references: [id])
  travelAssignments StudentTravelAssignment[]
  termExceptions    TermException[]

  @@map("establishments_term_periods")
}

model Staff {
  id               Int       @id @default(autoincrement()) @map("staff_id")
  firstName        String    @map("first_name") @db.VarChar(50)
  lastName         String    @map("last_name") @db.VarChar(50)
  email            String    @unique @db.VarChar(100)
  phone            String?   @db.VarChar(20)
  role             String?   @db.VarChar(50)
  isActive         Boolean   @default(true) @map("is_active")
  password         String?   @db.VarChar(255) // Added for staff authentication
  resetToken       String?   @map("reset_token") @db.VarChar(255) // Added for password reset
  resetTokenExpiry DateTime? @map("reset_token_expiry") // Added for password reset
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")

  // Relations
  contactNotes           StudentContactNote[]
  communications         StudentContactCommunication[]
  addresses              StudentContactAddress[]
  codeAssignmentsCreated StudentCodeAssignment[]            @relation("CreatedBy")
  codeAssignmentsUpdated StudentCodeAssignment[]            @relation("UpdatedBy")
  auditLogs              AuditLog[]
  medicalRecordsCreated  StudentsMedical[]                  @relation("MedicalCreatedBy")
  medicalRecordsUpdated  StudentsMedical[]                  @relation("MedicalUpdatedBy")
  reportedIncidents      ContractIncident[]
  budgetRatesCreated     BudgetCodeRate[]                   @relation("BudgetRateCreatedBy")
  budgetRatesUpdated     BudgetCodeRate[]                   @relation("BudgetRateUpdatedBy")
  contractBudgetCreated  OperatorContractBudgetAssignment[] @relation("ContractBudgetCreatedBy")
  contractBudgetUpdated  OperatorContractBudgetAssignment[] @relation("ContractBudgetUpdatedBy")

  @@map("staff")
}

model EligibilityCode {
  code        String   @id @db.VarChar(20)
  name        String   @db.VarChar(100)
  description String?  @db.Text
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  codeAssignments StudentCodeAssignment[]

  @@map("eligibility_codes")
}

model BudgetCode {
  code        String   @id @db.VarChar(20)
  name        String   @db.VarChar(100)
  description String?  @db.Text
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  codeAssignments     StudentCodeAssignment[]
  rates               BudgetCodeRate[]
  contractAssignments OperatorContractBudgetAssignment[]

  @@map("budget_codes")
}

model BudgetCodeRate {
  rateId    Int       @id @default(autoincrement()) @map("rate_id")
  code      String    @db.VarChar(20)
  validFrom DateTime  @map("valid_from") @db.Date
  validTo   DateTime? @map("valid_to") @db.Date
  rate      Decimal   @db.Decimal(10, 2)
  reason    String?   @db.Text
  createdBy Int?      @map("created_by")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedBy Int?      @map("updated_by")
  updatedAt DateTime  @updatedAt @map("updated_at")

  // Relations
  budgetCode     BudgetCode @relation(fields: [code], references: [code])
  createdByStaff Staff?     @relation("BudgetRateCreatedBy", fields: [createdBy], references: [id])
  updatedByStaff Staff?     @relation("BudgetRateUpdatedBy", fields: [updatedBy], references: [id])

  @@map("budget_code_rates")
}

model FundingCode {
  code        String   @id @db.VarChar(20)
  name        String   @db.VarChar(100)
  description String?  @db.Text
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  codeAssignments StudentCodeAssignment[]

  @@map("funding_codes")
}

model StudentCodeAssignment {
  id              Int       @id @default(autoincrement()) @map("assignment_id")
  studentId       Int       @map("student_id")
  eligibilityCode String    @map("eligibility_code") @db.VarChar(20)
  budgetCode      String    @map("budget_code") @db.VarChar(20)
  fundingCode     String    @map("funding_code") @db.VarChar(20)
  validFrom       DateTime  @map("valid_from") @db.Date
  validTo         DateTime? @map("valid_to") @db.Date
  createdBy       Int       @map("created_by")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedBy       Int?      @map("updated_by")
  updatedAt       DateTime  @updatedAt @map("updated_at")

  // Relations
  student        Student         @relation(fields: [studentId], references: [id])
  eligibility    EligibilityCode @relation(fields: [eligibilityCode], references: [code])
  budget         BudgetCode      @relation(fields: [budgetCode], references: [code])
  funding        FundingCode     @relation(fields: [fundingCode], references: [code])
  createdByStaff Staff           @relation("CreatedBy", fields: [createdBy], references: [id])
  updatedByStaff Staff?          @relation("UpdatedBy", fields: [updatedBy], references: [id])

  @@map("student_code_assignments")
}

model AuditLog {
  id        Int      @id @default(autoincrement()) @map("audit_id")
  entity    String   @db.VarChar(100)
  entityId  Int      @map("entity_id")
  operation String   @db.VarChar(10)
  changedBy Int      @map("changed_by")
  changedAt DateTime @default(now()) @map("changed_at")
  oldData   Json?    @map("old_data")
  newData   Json?    @map("new_data")

  // Relations
  staff Staff @relation(fields: [changedBy], references: [id])

  @@map("audit_log")
}

model StudentsMedical {
  id               Int      @id @default(autoincrement()) @map("medical_id")
  studentId        Int      @map("student_id")
  severity         String?  @db.VarChar(10) // Low / Medium / High
  requiresPa       Boolean? @map("requires_pa")
  assignedPaName   String?  @map("assigned_pa_name") @db.VarChar(100)
  assignedPaPhone  String?  @map("assigned_pa_phone") @db.VarChar(20)
  riskLevel        String?  @map("risk_level") @db.VarChar(10) // None / Low / Medium / High
  travelAloneAlert Boolean? @map("travel_alone_alert")
  violenceAlert    Boolean? @map("violence_alert")
  notes            String?  @db.Text
  createdBy        Int      @map("created_by")
  updatedBy        Int?     @map("updated_by")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  // Relations
  student        Student                       @relation(fields: [studentId], references: [id])
  createdByStaff Staff                         @relation("MedicalCreatedBy", fields: [createdBy], references: [id])
  updatedByStaff Staff?                        @relation("MedicalUpdatedBy", fields: [updatedBy], references: [id])
  conditions     StudentsMedicalConditions[]
  equipment      StudentsMedicalEquipment[]
  trainingTags   StudentsMedicalTrainingTags[]

  @@map("students_medical")
}

model StudentsMedicalConditions {
  id        Int     @id @default(autoincrement())
  medicalId Int     @map("medical_id")
  condition String  @db.VarChar(100) // e.g., ASD, Asthma
  details   String? @db.Text // Optional notes per condition

  // Relations
  medical StudentsMedical @relation(fields: [medicalId], references: [id])

  @@map("students_medical_conditions")
}

model StudentsMedicalEquipment {
  id            Int     @id @default(autoincrement())
  medicalId     Int     @map("medical_id")
  equipmentType String  @map("equipment_type") @db.VarChar(100) // e.g., Car Seat
  notes         String? @db.Text // e.g., 5-point harness

  // Relations
  medical StudentsMedical @relation(fields: [medicalId], references: [id])

  @@map("students_medical_equipment")
}

model StudentsMedicalTrainingTags {
  id        Int    @id @default(autoincrement())
  medicalId Int    @map("medical_id")
  tag       String @db.VarChar(100) // e.g., Epilepsy Response

  // Relations
  medical StudentsMedical @relation(fields: [medicalId], references: [id])

  @@map("students_medical_training_tags")
}

model TravelLocation {
  id               Int       @id @default(autoincrement()) @map("location_id")
  assignmentId     Int       @map("assignment_id")
  type             String    @db.VarChar(10) // "pickup" or "dropoff"
  isSchool         Boolean   @map("is_school")
  estabId          Int?      @map("estab_id") // FK when is_school = true
  addrId           Int?      @map("addr_id") // FK when is_school = false
  addressLine1     String?   @map("address_line1") @db.VarChar(255)
  addressLine2     String?   @map("address_line2") @db.VarChar(255)
  postcode         String?   @db.VarChar(10)
  departAfterTime  DateTime? @map("depart_after_time") @db.Time
  arriveBeforeTime DateTime? @map("arrive_before_time") @db.Time

  // Relations
  assignment     StudentTravelAssignment @relation(fields: [assignmentId], references: [id])
  establishment  Establishment?          @relation(fields: [estabId], references: [id])
  contactAddress StudentContactAddress?  @relation(fields: [addrId], references: [id])

  @@map("travel_locations")
}

model SchoolTermTime {
  id               Int      @id @default(autoincrement()) @map("term_time_id")
  estabId          Int      @map("estab_id")
  weekday          String   @db.VarChar(10) // "Monday" ... "Friday"
  startTime        DateTime @map("start_time") @db.Time
  endTime          DateTime @map("end_time") @db.Time
  acceptBeforeMins Int?     @map("accept_before_mins")
  acceptAfterMins  Int?     @map("accept_after_mins")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  // Relations
  establishment Establishment @relation(fields: [estabId], references: [id])

  @@map("school_term_times")
}

model TermException {
  id            Int      @id @default(autoincrement()) @map("exception_id")
  termId        Int      @map("term_id")
  exceptionType String   @map("exception_type") @db.VarChar(20) // "Inset Day", "Holiday", "Other"
  startDate     DateTime @map("start_date") @db.Date
  endDate       DateTime @map("end_date") @db.Date
  description   String?  @db.VarChar(255)
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // Relations
  termPeriod EstablishmentTermPeriod @relation(fields: [termId], references: [id])

  @@map("term_exceptions")
}

model TravelRouteMetric {
  id            Int      @id @default(autoincrement()) @map("metric_id")
  assignmentId  Int      @map("assignment_id")
  distanceMiles Decimal? @map("distance_miles") @db.Decimal(5, 2)
  durationText  String?  @map("duration_text") @db.VarChar(50)
  computedAt    DateTime @map("computed_at")

  // Relations
  assignment StudentTravelAssignment @relation(fields: [assignmentId], references: [id])

  @@map("travel_route_metrics")
}

model StudentTravelLog {
  id                  Int       @id @default(autoincrement()) @map("log_id")
  assignmentId        Int       @map("assignment_id")
  journeyDate         DateTime  @map("journey_date") @db.Date
  runType             String    @map("run_type") @db.VarChar(2) // "AM" or "PM"
  status              String?   @db.VarChar(20) // "Confirmed", "Missed", "Holiday", etc.
  pickupTimeOverride  DateTime? @map("pickup_time_override") @db.Time
  dropoffTimeOverride DateTime? @map("dropoff_time_override") @db.Time
  notes               Json? // JSONB for notes or separate relation
  createdAt           DateTime  @default(now()) @map("created_at")
  updatedAt           DateTime  @updatedAt @map("updated_at")

  // Relations
  assignment StudentTravelAssignment @relation(fields: [assignmentId], references: [id])

  @@map("student_travel_logs")
}

model StudentTravelDay {
  id           Int @id @default(autoincrement())
  assignmentId Int @map("assignment_id")
  dayOfWeek    Int @map("day_of_week") // 1-7 (Monday-Sunday)

  // Relations
  assignment StudentTravelAssignment @relation(fields: [assignmentId], references: [id])

  @@map("student_travel_days")
}

model IncidentType {
  id        Int      @id @default(autoincrement()) @map("incident_type_id")
  code      String   @unique @db.VarChar(20)
  name      String   @db.VarChar(50)
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  incidents ContractIncident[]

  @@map("incident_types")
}

model ContractIncident {
  id             Int      @id @default(autoincrement()) @map("incident_id")
  contractId     Int      @map("contract_id")
  incidentTypeId Int      @map("incident_type_id")
  severity       String   @db.VarChar(20)
  title          String   @db.VarChar(100)
  description    String   @db.Text
  reportedBy     Int      @map("reported_by")
  occurredAt     DateTime @map("occurred_at")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // Relations
  contract     OperatorContract @relation(fields: [contractId], references: [id])
  incidentType IncidentType     @relation(fields: [incidentTypeId], references: [id])
  reporter     Staff            @relation(fields: [reportedBy], references: [id])

  @@map("contract_incidents")
}
