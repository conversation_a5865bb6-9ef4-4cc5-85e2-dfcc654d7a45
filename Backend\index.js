// /index.js
require('dotenv').config();
const express = require('express');
const cors = require('cors');

console.log('Starting server...');

const app = express();

try {
  console.log('Loading routes...');

  // Import routes
  const authRoutes = require('./routes/authRoutes');
  const studentRoutes = require('./routes/studentRoutes');
  const utilityRoutes = require('./routes/utilityRoutes');
  const contactRoutes = require('./routes/contactRoutes');
  const codeRoutes = require('./routes/codeRoutes');
  const medicalRoutes = require('./routes/medicalRoutes');
  const travelRoutes = require('./routes/travelRoutes');
  const contractRoutes = require('./routes/contractRoutes');
  const financeRoutes = require('./routes/financeRoutes');

  console.log('All routes loaded successfully!');

// Middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000', 'http://127.0.0.1:5173', 'http://127.0.0.1:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());

// Logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

  // Routes
  app.use('/api/auth', authRoutes);
  app.use('/api/students', studentRoutes);
  app.use('/api/utility', utilityRoutes);
  app.use('/api/contacts', contactRoutes);
  app.use('/api/codes', codeRoutes);
  app.use('/api/medical', medicalRoutes);
  app.use('/api/travel', travelRoutes);
  app.use('/api/contracts', contractRoutes);
  app.use('/api/finances', financeRoutes);

  console.log('Routes registered successfully!');

} catch (error) {
  console.error('Error loading routes:', error);
  process.exit(1);
}

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err.stack);
  res.status(500).json({
    success: false,
    message: 'Internal server error'
  });
});

// Root route
app.get('/', (req, res) => {
  res.send('Student Transport Management API is running');
});


// Start server
const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});