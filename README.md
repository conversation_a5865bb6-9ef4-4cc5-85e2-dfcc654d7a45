# Transport Management System

A comprehensive web application for managing student transport services, built with React frontend and Node.js backend using PostgreSQL database.

## Prerequisites

Before setting up the project, ensure you have the following installed:

1. **Node.js** (version 18.0.0 or higher)
   - Download from: https://nodejs.org/
   - Verify installation: `node --version`

2. **npm** (comes with Node.js)
   - Verify installation: `npm --version`

3. **PostgreSQL** (version 12 or higher)
   - Download from: https://www.postgresql.org/download/
   - During installation, remember the password you set for the `postgres` user
   - Verify installation: `psql --version`

4. **Git** (optional, for version control)
   - Download from: https://git-scm.com/

## Project Structure

```
TransportManagementSystem/
├── Backend/                 # Node.js API server
│   ├── prisma/             # Database schema and migrations
│   ├── controllers/        # API controllers
│   ├── routes/            # API routes
│   ├── middlewares/       # Express middlewares
│   └── package.json       # Backend dependencies
├── Frontend/              # React application
│   ├── src/              # Source code
│   ├── public/           # Static assets
│   └── package.json      # Frontend dependencies
└── README.md             # This file
```

## Quick Setup (Automated)

For a quick automated setup, you can use the provided setup scripts:

### Windows Users:
```bash
# Run the setup script (Command Prompt)
setup.bat

# OR run with PowerShell
.\setup.ps1
```

### Manual Setup (Step by Step)

If you prefer manual setup or the automated script fails, follow these detailed steps:

### Step 1: Database Setup

1. **Start PostgreSQL Service**
   - On Windows: Open Services and start "postgresql-x64-xx" service
   - On macOS: `brew services start postgresql`
   - On Linux: `sudo systemctl start postgresql`

2. **Create Database**
   ```bash
   # Connect to PostgreSQL as postgres user
   psql -U postgres

   # Create the database
   CREATE DATABASE transport_mngmnt_system;

   # Exit psql
   \q
   ```

3. **Update Database Connection**
   - Open `Backend/.env` file
   - Update the `DATABASE_URL` with your PostgreSQL credentials:
   ```
   DATABASE_URL=postgresql://postgres:YOUR_PASSWORD@localhost:5432/transport_mngmnt_system
   ```
   - Replace `YOUR_PASSWORD` with your PostgreSQL password

### Step 2: Backend Setup

1. **Navigate to Backend directory**
   ```bash
   cd Backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Generate Prisma Client**
   ```bash
   npx prisma generate
   ```

4. **Run database migrations**
   ```bash
   npx prisma migrate deploy
   ```

5. **Seed the database with initial data**
   ```bash
   npm run seed
   ```

### Step 3: Frontend Setup

1. **Open a new terminal and navigate to Frontend directory**
   ```bash
   cd Frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

### Step 4: Running the Application

You need to run both backend and frontend servers simultaneously.

#### Option A: Using Start Scripts (Recommended)
```bash
# Terminal 1 - Start Backend
start-backend.bat

# Terminal 2 - Start Frontend
start-frontend.bat
```

#### Option B: Manual Commands
```bash
# Terminal 1 - Backend Server
cd Backend
npm start
```
The backend server will start on http://localhost:8000

```bash
# Terminal 2 - Frontend Development Server
cd Frontend
npm run dev
```
The frontend will start on http://localhost:5173

### Step 5: Access the Application

1. Open your web browser
2. Navigate to: http://localhost:5173
3. Use the following test credentials to login:
   - **Email**: <EMAIL>
   - **Password**: admin123

## Default Test Accounts

The seeded database includes these test accounts:

| Email | Password | Role |
|-------|----------|------|
| <EMAIL> | admin123 | Admin |
| <EMAIL> | admin123 | Manager |

## Troubleshooting

### Common Issues and Solutions

1. **CORS Error / Preflight Issues**
   - Ensure both frontend and backend are running
   - Check that backend is running on port 8000
   - Clear browser cache and cookies

2. **Database Connection Error**
   - Verify PostgreSQL is running
   - Check DATABASE_URL in Backend/.env
   - Ensure database exists and credentials are correct

3. **Port Already in Use**
   - Backend (8000): Kill process using `netstat -ano | findstr :8000` then `taskkill /PID <PID> /F`
   - Frontend (5173): Kill process or use different port

4. **Prisma Client Error**
   - Run `npx prisma generate` in Backend directory
   - Ensure database migrations are applied

5. **Module Not Found Errors**
   - Delete node_modules and package-lock.json
   - Run `npm install` again

### Environment Variables

Ensure these files exist with correct values:

**Backend/.env:**
```
DATABASE_URL=postgresql://postgres:YOUR_PASSWORD@localhost:5432/transport_mngmnt_system
JWT_SECRET=your_jwt_secret_key
PORT=8000
```

**Frontend/.env:**
```
VITE_BACKEND_URL=http://localhost:8000/api
```

## Development Commands

### Backend Commands
```bash
npm start          # Start development server with nodemon
npm run seed       # Seed database with test data
npx prisma studio  # Open Prisma Studio (database GUI)
npx prisma migrate dev  # Create and apply new migration
```

### Frontend Commands
```bash
npm run dev        # Start development server
npm run build      # Build for production
npm run preview    # Preview production build
npm run lint       # Run ESLint
```

## Features

- **Student Management**: Add, edit, and manage student records
- **Contact Management**: Manage student contacts and addresses
- **Transport Planning**: Assign students to transport routes
- **Medical Records**: Track student medical needs and requirements
- **Contract Management**: Manage transport operator contracts
- **Incident Reporting**: Track and manage transport incidents
- **Financial Tracking**: Budget codes and financial management
- **User Authentication**: Secure login system for staff

## Technology Stack

- **Frontend**: React 19, Vite, TailwindCSS, React Router
- **Backend**: Node.js, Express.js, Prisma ORM
- **Database**: PostgreSQL
- **Authentication**: JWT tokens
- **Styling**: TailwindCSS

## Support

If you encounter any issues during setup:

1. Check the troubleshooting section above
2. Ensure all prerequisites are properly installed
3. Verify that all environment variables are correctly set
4. Make sure both servers are running simultaneously

For additional help, check the console logs in both terminal windows for specific error messages.