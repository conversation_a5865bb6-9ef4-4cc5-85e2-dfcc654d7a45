# Transport Management System

## Prerequisites

1. **Node.js** (version 18.0.0 or higher) - Download from: https://nodejs.org/
2. **PostgreSQL** (version 12 or higher) - Download from: https://www.postgresql.org/download/

## Setup Instructions

### Option 1: Automated Setup (Recommended)
```bash
.\setup.ps1
```

### Option 2: Manual Setup

1. **Create Database**
   ```bash
   psql -U postgres
   CREATE DATABASE transport_mngmnt_system;
   \q
   ```

2. **Update Backend/.env with your PostgreSQL password**
   ```
   DATABASE_URL=postgresql://postgres:YOUR_PASSWORD@localhost:5432/transport_mngmnt_system
   ```

3. **Backend Setup**
   ```bash
   cd Backend
   npm install
   npx prisma generate
   npx prisma migrate deploy
   npm run seed
   ```

4. **Frontend Setup**
   ```bash
   cd Frontend
   npm install
   ```

## Running the Application

**Terminal 1 - Backend:**
```bash
cd Backend
npm start
```

**Terminal 2 - Frontend:**
```bash
cd Frontend
npm run dev
```

**Access:** http://localhost:5173

**Login Credentials:**
- Email: <EMAIL>
- Password: admin123

## Troubleshooting

- **Database Error**: Ensure PostgreSQL is running and DATABASE_URL is correct in Backend/.env
- **CORS Error**: Make sure both servers are running on correct ports (Backend: 8000, Frontend: 5173)
- **Module Errors**: Delete node_modules folders and run `npm install` again